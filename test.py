import os
os.environ['http_proxy'] = "http://z00456099:<EMAIL>:8080/"
os.environ['https_proxy'] = "http://z00456099:<EMAIL>:8080/"

# !/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高德API导航路线沿途POI搜索 - 完整优化版本
支持精确多边形缓冲区、多种POI类型、错误重试等功能

作者: z00456099 + AI
版本: 2.0
更新: 2025-06-20
"""

import requests
import json
import math
import time
from typing import List, Tuple, Dict, Any, Optional


class CompleteRoutePoiSearch:
    """完整的高德地图路线沿途POI搜索类"""

    # 高德地图API配置
    BASE_URL = "https://restapi.amap.com"

    # POI类型映射
    POI_TYPES = {
        "加油站": "010300",
        "餐厅": "050000",
        "酒店": "100000",
        "医院": "090100",
        "银行": "160000",
        "ATM": "160100",
        "超市": "060000",
        "停车场": "150900",
        "充电站": "010301",
        "服务区": "180300",
        "商场": "060100",
        "药店": "090200",
        "便利店": "060200"
    }

    # 路径策略
    STRATEGIES = {
        "速度优先": 0,
        "费用优先": 1,
        "距离优先": 2,
        "不走高速": 3
    }

    def __init__(self, api_key: str):
        """
        初始化搜索类
        :param api_key: 高德地图API密钥
        """
        if not api_key or api_key == "your_amap_api_key_here":
            raise ValueError("请提供有效的高德地图API Key")

        self.api_key = api_key
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'RoutePoiSearch/2.0'
        })

    def _make_request(self, url: str, params: Dict[str, Any],
                      max_retries: int = 3, timeout: int = 30) -> Dict[str, Any]:
        """
        发送HTTP请求，包含重试机制和错误处理
        """
        params['key'] = self.api_key

        for attempt in range(max_retries):
            try:
                response = self.session.get(url, params=params, timeout=timeout)
                response.raise_for_status()

                data = response.json()

                # 检查高德API响应状态
                if data.get('status') == '0':
                    error_msg = data.get('info', '未知错误')
                    if attempt == max_retries - 1:
                        return {"status": "0", "info": f"API错误: {error_msg}"}
                    time.sleep(1)
                    continue

                return data

            except requests.RequestException as e:
                if attempt == max_retries - 1:
                    return {"status": "0", "info": f"网络请求失败: {str(e)}"}
                time.sleep(1)

        return {"status": "0", "info": "请求失败"}

    def get_driving_route(self, origin: str, destination: str,
                          strategy: str = "速度优先",
                          waypoints: str = "") -> Dict[str, Any]:
        """
        获取驾车路线规划
        :param origin: 起点坐标 "经度,纬度"
        :param destination: 终点坐标 "经度,纬度"
        :param strategy: 路径策略
        :param waypoints: 途经点坐标，多个点用|分隔
        :return: 路线规划结果
        """
        url = f"{self.BASE_URL}/v3/direction/driving"
        params = {
            'origin': origin,
            'destination': destination,
            'extensions': 'all',
            'strategy': self.STRATEGIES.get(strategy, 0),
            'waypoints': waypoints
        }

        return self._make_request(url, params)

    def parse_route_coordinates(self, route_data: Dict[str, Any]) -> List[Tuple[float, float]]:
        """
        解析路线坐标点
        :param route_data: 路线规划数据
        :return: 坐标点列表
        """
        coordinates = []

        if route_data.get('status') == '1' and route_data.get('route'):
            paths = route_data['route']['paths']
            for path in paths:
                for step in path['steps']:
                    polyline = step['polyline']
                    # 解析polyline坐标字符串
                    coord_pairs = polyline.split(';')
                    for coord_pair in coord_pairs:
                        if coord_pair.strip():
                            try:
                                lng, lat = map(float, coord_pair.split(','))
                                coordinates.append((lng, lat))
                            except ValueError:
                                continue

        return coordinates

    def sample_route_coordinates(self, coordinates: List[Tuple[float, float]],
                                 max_points: int = 50,
                                 sampling_method: str = "douglas_peucker") -> List[Tuple[float, float]]:
        """
        对路线坐标进行采样，减少点数以避免URL长度超限
        :param coordinates: 原始坐标点列表
        :param max_points: 最大保留点数
        :param sampling_method: 采样方法 ("uniform", "distance", "douglas_peucker")
        :return: 采样后的坐标点列表
        """
        if len(coordinates) <= max_points:
            return coordinates

        if sampling_method == "uniform":
            return self._uniform_sampling(coordinates, max_points)
        elif sampling_method == "distance":
            return self._distance_based_sampling(coordinates, max_points)
        elif sampling_method == "douglas_peucker":
            return self._douglas_peucker_sampling(coordinates, max_points)
        else:
            return self._uniform_sampling(coordinates, max_points)

    def _uniform_sampling(self, coordinates: List[Tuple[float, float]],
                          max_points: int) -> List[Tuple[float, float]]:
        """均匀采样：等间隔选择点"""
        if len(coordinates) <= max_points:
            return coordinates

        # 确保包含起点和终点
        step = (len(coordinates) - 1) / (max_points - 1)
        sampled = [coordinates[0]]  # 起点

        for i in range(1, max_points - 1):
            index = int(round(i * step))
            sampled.append(coordinates[index])

        sampled.append(coordinates[-1])  # 终点
        return sampled

    def _distance_based_sampling(self, coordinates: List[Tuple[float, float]],
                                 max_points: int) -> List[Tuple[float, float]]:
        """基于距离的采样：保持重要的转折点"""
        if len(coordinates) <= max_points:
            return coordinates

        # 计算每个点到前一个点的距离
        distances = [0]  # 起点距离为0
        for i in range(1, len(coordinates)):
            dist = self._calculate_distance(coordinates[i - 1], coordinates[i])
            distances.append(dist)

        # 计算累积距离
        cumulative_distances = [sum(distances[:i + 1]) for i in range(len(distances))]
        total_distance = cumulative_distances[-1]

        # 按等距离间隔采样
        sampled = [coordinates[0]]  # 起点
        target_distance = total_distance / (max_points - 1)

        for i in range(1, max_points - 1):
            target = i * target_distance
            # 找到最接近目标距离的点
            closest_idx = min(range(len(cumulative_distances)),
                              key=lambda x: abs(cumulative_distances[x] - target))
            sampled.append(coordinates[closest_idx])

        sampled.append(coordinates[-1])  # 终点
        return sampled

    def _douglas_peucker_sampling(self, coordinates: List[Tuple[float, float]],
                                  max_points: int) -> List[Tuple[float, float]]:
        """Douglas-Peucker算法：保持路线形状特征"""
        if len(coordinates) <= max_points:
            return coordinates

        # 简化版Douglas-Peucker算法
        def douglas_peucker(points, epsilon):
            if len(points) <= 2:
                return points

            # 找到距离起终点连线最远的点
            start, end = points[0], points[-1]
            max_distance = 0
            max_index = 0

            for i in range(1, len(points) - 1):
                distance = self._point_to_line_distance(points[i], start, end)
                if distance > max_distance:
                    max_distance = distance
                    max_index = i

            # 如果最大距离小于阈值，返回起终点
            if max_distance < epsilon:
                return [start, end]

            # 递归处理两段
            left_points = douglas_peucker(points[:max_index + 1], epsilon)
            right_points = douglas_peucker(points[max_index:], epsilon)

            # 合并结果（去除重复的中间点）
            return left_points[:-1] + right_points

        # 动态调整epsilon直到点数满足要求
        epsilon = 0.0001  # 初始阈值
        result = coordinates

        while len(result) > max_points and epsilon < 0.01:
            result = douglas_peucker(coordinates, epsilon)
            epsilon *= 1.5

        # 如果还是太多点，使用均匀采样作为后备
        if len(result) > max_points:
            result = self._uniform_sampling(result, max_points)

        return result

    def _calculate_distance(self, point1: Tuple[float, float],
                            point2: Tuple[float, float]) -> float:
        """计算两点间的欧几里得距离"""
        return math.sqrt((point1[0] - point2[0]) ** 2 + (point1[1] - point2[1]) ** 2)

    def _point_to_line_distance(self, point: Tuple[float, float],
                                line_start: Tuple[float, float],
                                line_end: Tuple[float, float]) -> float:
        """计算点到直线的距离"""
        x0, y0 = point
        x1, y1 = line_start
        x2, y2 = line_end

        # 如果起终点相同，返回点到点的距离
        if x1 == x2 and y1 == y2:
            return math.sqrt((x0 - x1) ** 2 + (y0 - y1) ** 2)

        # 点到直线距离公式
        numerator = abs((y2 - y1) * x0 - (x2 - x1) * y0 + x2 * y1 - y2 * x1)
        denominator = math.sqrt((y2 - y1) ** 2 + (x2 - x1) ** 2)

        return numerator / denominator if denominator > 0 else 0

    def create_precise_buffer_polygon(self, coordinates: List[Tuple[float, float]],
                                      buffer_distance: float,
                                      max_vertices: int = 100,
                                      sampling_method: str = "douglas_peucker") -> List[Tuple[float, float]]:
        """
        创建精确的路线缓冲区多边形，支持采样以控制顶点数量
        :param coordinates: 路线坐标点
        :param buffer_distance: 缓冲区距离（米）
        :param max_vertices: 最大顶点数量（避免URL过长）
        :param sampling_method: 采样方法
        :return: 多边形坐标点
        """
        if len(coordinates) < 2:
            return []

        # 1. 先进行路线采样以控制复杂度
        original_count = len(coordinates)

        # 计算合适的采样点数（考虑多边形会有2倍的点数）
        max_route_points = max_vertices // 2 - 2  # 预留一些空间

        if len(coordinates) > max_route_points:
            print(f"📉 路线采样: {original_count} → {max_route_points} 个点 (方法: {sampling_method})")
            coordinates = self.sample_route_coordinates(
                coordinates, max_route_points, sampling_method
            )

        # 2. 创建缓冲区多边形
        left_points = []  # 路线左侧的点
        right_points = []  # 路线右侧的点

        # 为每个路段创建缓冲区
        for i in range(len(coordinates) - 1):
            lng1, lat1 = coordinates[i]
            lng2, lat2 = coordinates[i + 1]

            # 计算地理偏移量（考虑地球曲率）
            lat_offset = buffer_distance / 111000  # 1度纬度约111km
            lng_offset = buffer_distance / (111000 * math.cos(math.radians((lat1 + lat2) / 2)))

            # 计算路段方向向量
            dx = lng2 - lng1
            dy = lat2 - lat1
            length = math.sqrt(dx * dx + dy * dy)

            if length > 0:
                # 计算垂直于路段的单位向量
                perp_x = -dy / length * lng_offset
                perp_y = dx / length * lat_offset

                # 添加左右两侧的缓冲点
                left_points.append((lng1 + perp_x, lat1 + perp_y))
                right_points.append((lng1 - perp_x, lat1 - perp_y))

        # 处理最后一个点
        if coordinates and len(coordinates) >= 2:
            last_lng, last_lat = coordinates[-1]
            prev_lng, prev_lat = coordinates[-2]

            dx = last_lng - prev_lng
            dy = last_lat - prev_lat
            length = math.sqrt(dx * dx + dy * dy)

            if length > 0:
                lat_offset = buffer_distance / 111000
                lng_offset = buffer_distance / (111000 * math.cos(math.radians(last_lat)))
                perp_x = -dy / length * lng_offset
                perp_y = dx / length * lat_offset

                left_points.append((last_lng + perp_x, last_lat + perp_y))
                right_points.append((last_lng - perp_x, last_lat - perp_y))

        # 构建完整多边形：左侧点 + 反向右侧点
        polygon = left_points + right_points[::-1]

        # 闭合多边形
        if polygon and polygon[0] != polygon[-1]:
            polygon.append(polygon[0])

        # 3. 检查最终顶点数量
        final_vertices = len(polygon) - 1  # 减去闭合点
        if final_vertices > max_vertices:
            print(f"⚠️  多边形顶点数 ({final_vertices}) 仍超过限制 ({max_vertices})")

        return polygon

    def search_poi_in_polygon(self, polygon: List[Tuple[float, float]],
                              keywords: str, poi_type: str = "",
                              page_size: int = 20, page_num: int = 1,
                              max_url_length: int = 8000) -> Dict[str, Any]:
        """
        在多边形区域内搜索POI，自动处理URL长度限制
        :param polygon: 多边形坐标点列表
        :param keywords: 搜索关键词
        :param poi_type: POI类型码
        :param page_size: 每页结果数量
        :param page_num: 页码
        :param max_url_length: 最大URL长度限制
        :return: 搜索结果
        """
        if len(polygon) < 3:
            return {"status": "0", "info": "多边形坐标点不足"}

        # 构建基础参数
        base_params = {
            'keywords': keywords,
            'extensions': 'all',
            'page_size': min(page_size, 25),  # 高德API限制
            'page_num': page_num
        }

        if poi_type:
            base_params['types'] = poi_type

        # 检查URL长度并自动调整多边形复杂度
        original_polygon = polygon.copy()
        current_polygon = polygon

        while True:
            # 将多边形坐标转换为字符串格式
            polygon_str = "|".join([f"{lng:.6f},{lat:.6f}" for lng, lat in current_polygon])

            # 构建完整URL进行长度检查
            test_params = base_params.copy()
            test_params['polygon'] = polygon_str
            test_params['key'] = 'test_key'

            # 估算URL长度
            url_base = f"{self.BASE_URL}/v3/place/polygon"
            param_str = "&".join([f"{k}={v}" for k, v in test_params.items()])
            estimated_url_length = len(url_base) + len(param_str) + 100  # 预留空间

            if estimated_url_length <= max_url_length:
                break

            # URL太长，需要简化多边形
            current_vertices = len(current_polygon) - 1  # 减去闭合点
            if current_vertices <= 6:  # 最少保持6个顶点
                return {
                    "status": "0",
                    "info": f"多边形过于复杂，无法在URL长度限制内处理。当前顶点数: {current_vertices}"
                }

            # 减少顶点数量
            new_max_vertices = max(6, int(current_vertices * 0.8))
            print(f"🔧 URL过长，简化多边形: {current_vertices} → {new_max_vertices} 个顶点")

            # 重新采样多边形（去掉闭合点，采样后再加回）
            polygon_without_closure = current_polygon[:-1] if current_polygon[0] == current_polygon[
                -1] else current_polygon
            simplified = self._uniform_sampling(polygon_without_closure, new_max_vertices)

            # 重新闭合多边形
            if simplified and simplified[0] != simplified[-1]:
                simplified.append(simplified[0])

            current_polygon = simplified

        # 执行搜索
        final_params = base_params.copy()
        final_params['polygon'] = polygon_str

        # 记录多边形简化信息
        original_vertices = len(original_polygon) - 1
        final_vertices = len(current_polygon) - 1

        if original_vertices != final_vertices:
            print(f"📐 多边形已简化: {original_vertices} → {final_vertices} 个顶点")

        result = self._make_request(f"{self.BASE_URL}/v3/place/polygon", final_params)

        # 在结果中添加多边形信息
        if isinstance(result, dict):
            result['polygon_info'] = {
                'original_vertices': original_vertices,
                'final_vertices': final_vertices,
                'simplified': original_vertices != final_vertices,
                'estimated_url_length': estimated_url_length
            }

        return result

    def split_route_into_segments(self, coordinates: List[Tuple[float, float]],
                                  max_points_per_segment: int = 30) -> List[List[Tuple[float, float]]]:
        """
        将长路线分割成多个段，每段控制在指定点数内
        :param coordinates: 完整路线坐标
        :param max_points_per_segment: 每段最大点数
        :return: 分段后的坐标列表
        """
        if len(coordinates) <= max_points_per_segment:
            return [coordinates]

        segments = []
        overlap_points = 3  # 段间重叠点数，确保连续性

        start_idx = 0
        while start_idx < len(coordinates):
            # 计算当前段的结束索引
            end_idx = min(start_idx + max_points_per_segment, len(coordinates))

            # 提取当前段
            segment = coordinates[start_idx:end_idx]
            segments.append(segment)

            # 如果已经到达终点，退出循环
            if end_idx >= len(coordinates):
                break

            # 下一段的起始点要往前重叠几个点，确保连续性
            start_idx = end_idx - overlap_points

        print(f"📏 路线分段: 总计 {len(coordinates)} 个点 → {len(segments)} 段")
        for i, segment in enumerate(segments):
            print(f"   段 {i+1}: {len(segment)} 个点")

        return segments

    def search_poi_with_coordinates(self, coordinates: List[Tuple[float, float]],
                                   keywords: str, poi_type_name: str = "",
                                   buffer_distance: float = 1000,
                                   max_vertices: int = 80,
                                   sampling_method: str = "douglas_peucker") -> Dict[str, Any]:
        """
        基于已有坐标搜索POI（不重新进行路线规划）
        :param coordinates: 路线坐标点列表
        :param keywords: 搜索关键词
        :param poi_type_name: POI类型名称
        :param buffer_distance: 搜索缓冲区距离（米）
        :param max_vertices: 最大多边形顶点数
        :param sampling_method: 路线采样方法
        :return: 搜索结果
        """
        result = {
            "status": "0",
            "info": "",
            "polygon_info": {},
            "pois": [],
            "total_count": 0
        }

        try:
            if not coordinates or len(coordinates) < 2:
                result["info"] = "坐标点不足，无法创建搜索区域"
                return result

            # 创建搜索多边形
            polygon = self.create_precise_buffer_polygon(
                coordinates, buffer_distance, max_vertices, sampling_method
            )

            if not polygon:
                result["info"] = "无法创建搜索区域多边形"
                return result

            result["polygon_info"] = {
                "vertices_count": len(polygon) - 1,  # 减去闭合点
                "buffer_distance": buffer_distance,
                "max_vertices": max_vertices,
                "sampling_method": sampling_method,
                "original_route_points": len(coordinates)
            }

            # 确定POI类型
            poi_type = self.POI_TYPES.get(poi_type_name, "")

            # 搜索POI
            poi_result = self.search_poi_in_polygon(polygon, keywords, poi_type)

            if poi_result.get('status') == '1':
                result["status"] = "1"
                result["info"] = "搜索成功"
                result["pois"] = poi_result.get('pois', [])
                result["total_count"] = int(poi_result.get('count', 0))
            else:
                result["info"] = f"POI搜索失败: {poi_result.get('info', '未知错误')}"

        except Exception as e:
            result["info"] = f"搜索过程出错: {str(e)}"

        return result

    def search_along_route_segmented(self, origin: str, destination: str,
                                   keywords: str, poi_type_name: str = "",
                                   buffer_distance: float = 1000,
                                   strategy: str = "速度优先",
                                   waypoints: str = "",
                                   max_points_per_segment: int = 30,
                                   sampling_method: str = "douglas_peucker") -> Dict[str, Any]:
        """
        分段搜索沿途POI - 针对长路径的优化版本
        :param origin: 起点坐标
        :param destination: 终点坐标
        :param keywords: 搜索关键词
        :param poi_type_name: POI类型名称
        :param buffer_distance: 搜索缓冲区距离（米）
        :param strategy: 路径策略
        :param waypoints: 途经点
        :param max_points_per_segment: 每段最大点数
        :param sampling_method: 路线采样方法
        :return: 完整搜索结果
        """
        result = {
            "status": "0",
            "info": "",
            "search_params": {
                "origin": origin,
                "destination": destination,
                "keywords": keywords,
                "poi_type": poi_type_name,
                "buffer_distance": buffer_distance,
                "strategy": strategy,
                "segmented": True,
                "max_points_per_segment": max_points_per_segment
            },
            "route_info": {},
            "segment_info": {},
            "pois": [],
            "total_count": 0,
            "execution_time": 0
        }

        start_time = time.time()

        try:
            # 1. 获取路线规划
            print("🚗 正在获取路线规划...")
            route_data = self.get_driving_route(origin, destination, strategy, waypoints)

            if route_data.get('status') != '1':
                result["info"] = f"路线规划失败: {route_data.get('info', '未知错误')}"
                return result

            # 2. 解析路线信息
            route = route_data['route']
            path = route['paths'][0]

            result["route_info"] = {
                "distance_km": round(float(path['distance']) / 1000, 1),
                "duration_minutes": round(int(path['duration']) / 60, 1),
                "tolls_yuan": int(path.get('tolls', 0)),
                "traffic_lights": int(path.get('traffic_lights', 0)),
                "restriction": path.get('restriction', 0)
            }

            # 3. 解析路线坐标
            print("📍 正在解析路线坐标...")
            coordinates = self.parse_route_coordinates(route_data)

            if not coordinates:
                result["info"] = "无法解析路线坐标"
                return result

            print(f"📍 解析到 {len(coordinates)} 个路线坐标点")

            # 4. 判断是否需要分段
            if len(coordinates) <= max_points_per_segment * 2:
                print("📏 路线较短，使用单段搜索")
                # 直接使用已有坐标进行搜索，避免重复路线规划
                single_result = self.search_poi_with_coordinates(
                    coordinates, keywords, poi_type_name, buffer_distance,
                    max_points_per_segment * 2, sampling_method
                )

                # 将单段搜索结果转换为完整格式
                if single_result.get('status') == '1':
                    result["status"] = "1"
                    result["info"] = "单段搜索成功"
                    result["pois"] = single_result.get('pois', [])
                    result["total_count"] = single_result.get('total_count', 0)
                    result["polygon_info"] = single_result.get('polygon_info', {})
                else:
                    result["info"] = f"单段搜索失败: {single_result.get('info', '未知错误')}"

                result["execution_time"] = round(time.time() - start_time, 2)
                return result

            # 5. 分段处理
            segments = self.split_route_into_segments(coordinates, max_points_per_segment)

            result["segment_info"] = {
                "total_segments": len(segments),
                "original_points": len(coordinates),
                "max_points_per_segment": max_points_per_segment
            }

            # 6. 确定POI类型
            poi_type = self.POI_TYPES.get(poi_type_name, "")

            # 7. 逐段搜索POI（使用已有坐标，避免重复路线规划）
            all_pois = []
            poi_ids_seen = set()  # 用于去重

            for i, segment_coords in enumerate(segments):
                print(f"🔍 正在搜索第 {i+1}/{len(segments)} 段...")
                print(f"   段坐标点数: {len(segment_coords)}")

                # 直接使用坐标进行POI搜索，避免重复路线规划
                segment_result = self.search_poi_with_coordinates(
                    coordinates=segment_coords,
                    keywords=keywords,
                    poi_type_name=poi_type_name,
                    buffer_distance=buffer_distance,
                    max_vertices=80,
                    sampling_method=sampling_method
                )

                if segment_result.get('status') == '1':
                    segment_pois = segment_result.get('pois', [])

                    # 去重处理（基于POI的ID或坐标）
                    new_pois = []
                    for poi in segment_pois:
                        poi_id = poi.get('id')
                        poi_location = poi.get('location', '')

                        # 使用ID或坐标作为唯一标识
                        unique_key = poi_id if poi_id else poi_location

                        if unique_key and unique_key not in poi_ids_seen:
                            poi_ids_seen.add(unique_key)
                            poi['segment'] = i + 1  # 标记来自哪一段
                            new_pois.append(poi)

                    all_pois.extend(new_pois)
                    print(f"   第 {i+1} 段找到 {len(segment_pois)} 个POI，去重后新增 {len(new_pois)} 个")
                else:
                    print(f"   第 {i+1} 段搜索失败: {segment_result.get('info', '未知错误')}")

            # 8. 汇总结果
            if all_pois:
                result["status"] = "1"
                result["info"] = "分段搜索成功"
                result["pois"] = all_pois
                result["total_count"] = len(all_pois)
                print(f"✅ 分段搜索完成，总计找到 {len(all_pois)} 个不重复的POI")
            else:
                result["info"] = "所有段都未找到相关POI"

        except Exception as e:
            result["info"] = f"分段搜索过程出错: {str(e)}"

        finally:
            result["execution_time"] = round(time.time() - start_time, 2)

        return result

    def get_available_poi_types(self) -> Dict[str, str]:
        """获取支持的POI类型列表"""
        return self.POI_TYPES.copy()

    def get_available_strategies(self) -> Dict[str, int]:
        """获取支持的路径策略列表"""
        return self.STRATEGIES.copy()

    def format_results(self, search_result: Dict[str, Any], max_display: int = 10) -> str:
        """
        格式化搜索结果为可读文本，支持分段搜索结果
        :param search_result: 搜索结果
        :param max_display: 最大显示数量
        :return: 格式化的文本
        """
        if search_result["status"] != "1":
            return f"❌ 搜索失败: {search_result['info']}"

        output = []

        # 搜索参数
        params = search_result["search_params"]
        output.append("🔍 搜索参数:")
        output.append(f"   关键词: {params['keywords']}")
        output.append(f"   POI类型: {params['poi_type'] or '全部'}")
        output.append(f"   缓冲区: {params['buffer_distance']}米")
        output.append(f"   策略: {params['strategy']}")

        # 如果是分段搜索，显示分段信息
        if params.get('segmented'):
            output.append(f"   搜索方式: 分段搜索")
            output.append(f"   每段最大点数: {params.get('max_points_per_segment', 'N/A')}")

        # 路线信息
        route = search_result["route_info"]
        output.append(f"\n🚗 路线信息:")
        output.append(f"   距离: {route['distance_km']}公里")
        output.append(f"   时间: {route['duration_minutes']}分钟")
        if route['tolls_yuan'] > 0:
            output.append(f"   过路费: {route['tolls_yuan']}元")
        output.append(f"   红绿灯: {route['traffic_lights']}个")

        # 分段信息（如果是分段搜索）
        if 'segment_info' in search_result:
            segment = search_result["segment_info"]
            output.append(f"\n📏 分段信息:")
            output.append(f"   原始路线点数: {segment.get('original_points', 'N/A')}")
            output.append(f"   分段数量: {segment.get('total_segments', 'N/A')}")
            output.append(f"   每段最大点数: {segment.get('max_points_per_segment', 'N/A')}")

        # 搜索区域信息（单段搜索才有）
        elif 'polygon_info' in search_result:
            polygon = search_result["polygon_info"]
            output.append(f"\n📐 搜索区域:")
            output.append(f"   多边形顶点: {polygon['vertices_count']}个")
            output.append(f"   缓冲区距离: {polygon['buffer_distance']}米")

        # POI结果
        pois = search_result["pois"]
        total = search_result["total_count"]

        output.append(f"\n🎯 搜索结果: 共找到 {total} 个POI")

        if pois:
            output.append("")
            for i, poi in enumerate(pois[:max_display], 1):
                name = poi.get('name', 'N/A')
                address = poi.get('address', 'N/A')
                location = poi.get('location', 'N/A')
                tel = poi.get('tel', '')
                segment = poi.get('segment')  # 分段搜索中的段号

                poi_info = f"{i}. {name}"
                if segment:
                    poi_info += f" (第{segment}段)"
                output.append(poi_info)

                output.append(f"   📍 {address}")
                output.append(f"   🗺️  {location}")
                if tel:
                    output.append(f"   📞 {tel}")
                output.append("")

            if total > max_display:
                output.append(f"... 还有 {total - max_display} 个结果")

        # 执行时间
        output.append(f"\n⏱️  执行时间: {search_result['execution_time']}秒")

        return "\n".join(output)


def main():
    """主函数 - 使用示例"""
    # 请替换为您的高德地图API Key
    API_KEY = "82340bfd5e9cbc8b00c87009fae348d3"

    if API_KEY == "your_amap_api_key_here":
        print("❌ 请先在代码中配置您的高德地图API Key!")
        return

    try:
        # 创建搜索实例
        searcher = CompleteRoutePoiSearch(API_KEY)

        print("🗺️  高德地图沿途POI搜索演示")
        print("=" * 60)

        # 测试路线：上海到杭州（较长距离，适合分段搜索）
        origin = "121.604218,31.245483"  # 上海外滩
        destination = "120.108319,30.24046"  # 杭州西湖

        print("📍 测试路线：上海外滩 → 杭州西湖")
        print(f"📍 起点：{origin}")
        print(f"📍 终点：{destination}")

        # 方式2：分段搜索（推荐用于长路径）
        print("🔍 方式2：分段搜索（长路径优化）")
        print("-" * 40)
        result2 = searcher.search_along_route_segmented(
            origin=origin,
            destination=destination,
            keywords="服务区",
            poi_type_name="服务区",
            buffer_distance=1000,
            strategy="速度优先",
            max_points_per_segment=500  # 每段最多25个点
        )

        # 输出分段搜索的详细结果
        if result2.get('status') == '1':
            segment_info = result2.get('segment_info', {})
            print(f"✅ 分段搜索成功！")
            print(f"   总路线点数: {segment_info.get('original_points', 0)}")
            print(f"   分段数量: {segment_info.get('total_segments', 0)}")
            print(f"   每段最大点数: {segment_info.get('max_points_per_segment', 0)}")
            print(f"   找到POI总数: {result2.get('total_count', 0)}")
            print(f"   执行时间: {result2.get('execution_time', 0)}秒")

            # 显示前几个POI的详细信息
            pois = result2.get('pois', [])
            if pois:
                print(f"\n📍 前5个POI详情:")
                for i, poi in enumerate(pois[:5], 1):
                    name = poi.get('name', 'N/A')
                    address = poi.get('address', 'N/A')
                    segment = poi.get('segment', 'N/A')
                    print(f"   {i}. {name} (第{segment}段)")
                    print(f"      地址: {address}")
        else:
            print(f"❌ 分段搜索失败: {result2.get('info', '未知错误')}")

        print("\n" + "=" * 60)
        print("💡 使用建议:")
        print("   • 短距离路线（<100个坐标点）：使用 search_along_route()")
        print("   • 长距离路线（>100个坐标点）：使用 search_along_route_segmented()")
        print("   • 分段搜索可以避免URL长度限制，提高搜索成功率")
        print("   • 每段建议控制在20-30个坐标点以内")

    except Exception as e:
        print(f"❌ 程序执行出错: {str(e)}")


if __name__ == "__main__":
    main()
